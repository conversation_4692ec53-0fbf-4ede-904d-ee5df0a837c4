# JSON Execution Feature Test

This document demonstrates the new JSON execution feature in the chat interface.

## How it works

When a chat message contains JSON data, the system will automatically detect it and display an execution box with an "Execute" button, similar to the transaction preview cards.

## Test Cases

### 1. J<PERSON><PERSON> in Code Block

```json
{
  "type": "transfer",
  "amount": "1.5",
  "currency": "ETH",
  "to": "******************************************",
  "from": "0x8ba1f109551bD432803012645Hac136c22C4B4d8b6"
}
```

### 2. Configuration JSON

```json
{
  "type": "config_update",
  "settings": {
    "theme": "dark",
    "notifications": true,
    "autoExecute": false
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 3. Array JSON

```json
[
  {
    "id": 1,
    "action": "approve",
    "contract": "******************************************"
  },
  {
    "id": 2,
    "action": "transfer",
    "amount": "100"
  }
]
```

### 4. API Call JSON

```json
{
  "type": "api_call",
  "endpoint": "/api/tokens/balance",
  "method": "GET",
  "params": {
    "address": "******************************************",
    "chainId": 1
  }
}
```

## Features

- **Automatic Detection**: JSON blocks are automatically detected in chat messages
- **Visual Preview**: JSON content is displayed in a formatted, scrollable preview
- **Execution Tracking**: Once executed, the button changes to show "✓ Executed" state
- **Type Information**: Shows whether the JSON is an Object or Array
- **Size Information**: Displays the number of properties or items
- **Action Type**: If the JSON has a "type" field, it's displayed as the action type

## Usage

1. Send a message containing JSON (in code blocks or standalone)
2. The system will detect the JSON and show an execution card
3. Click the "Execute JSON Action" button to process the JSON
4. The button will change to "✓ Executed" to indicate completion
5. Check the browser console for execution logs

## Styling

The JSON execution cards use the same styling as transaction cards:
- Nebula-themed gradient background
- Primary color accents
- Consistent spacing and typography
- Responsive design
