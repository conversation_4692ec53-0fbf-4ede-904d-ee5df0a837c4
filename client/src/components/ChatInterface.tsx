import { useEffect, useRef, useState, useCallback } from "react";
import { ArrowLeftIcon, ArrowDownIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import ChatMessage from "@/components/ChatMessage";
import MessageInput from "@/components/MessageInput";

interface ChatInterfaceProps {
  messages: any[];
  onSendMessage: (message: string) => void;
  onBackToWelcome: () => void;
  isLoading?: boolean;
}

const ChatInterface = ({
  messages,
  onSendMessage,
  onBackToWelcome,
  isLoading = false,
}: ChatInterfaceProps) => {
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolledUp, setIsUserScrolledUp] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // Helper function to check if user is near bottom
  const isNearBottom = useCallback(() => {
    if (!chatContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    const threshold = 100; // pixels from bottom
    return scrollHeight - scrollTop - clientHeight < threshold;
  }, []);

  // Helper function to scroll to bottom smoothly
  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
      setIsUserScrolledUp(false);
      setShowScrollToBottom(false);
    }
  }, []);

  // Handle scroll events to detect user scrolling
  const handleScroll = useCallback(() => {
    if (!chatContainerRef.current) return;

    const nearBottom = isNearBottom();
    const userScrolledUp = !nearBottom;

    setIsUserScrolledUp(userScrolledUp);
    setShowScrollToBottom(userScrolledUp);

    // If user scrolled back near bottom, resume auto-scroll
    if (nearBottom && isUserScrolledUp) {
      setIsUserScrolledUp(false);
      setShowScrollToBottom(false);
    }
  }, [isNearBottom, isUserScrolledUp]);

  // Scroll to bottom when messages change (only if user hasn't scrolled up)
  useEffect(() => {
    if (messages && !isUserScrolledUp) {
      scrollToBottom();
    }
  }, [messages, isUserScrolledUp, scrollToBottom]);

  // Scroll to bottom when loading starts (only if user hasn't scrolled up)
  useEffect(() => {
    if (isLoading && !isUserScrolledUp) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [isLoading, isUserScrolledUp, scrollToBottom]);

  return (
    <div className="flex flex-col h-full">
      {/* Header with back button */}
      <div className="flex items-center p-3 sm:p-4 border-b border-border/50">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBackToWelcome}
          className="mr-2 sm:mr-3 hover:bg-muted min-h-[44px] px-3 sm:px-4"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1 sm:mr-2" />
          <span className="text-sm sm:text-base">New Chat</span>
        </Button>
      </div>

      {/* Chat Messages */}
      <div className="relative flex-1 min-h-0">
        <div
          ref={chatContainerRef}
          className="absolute inset-0 overflow-y-auto chat-container px-2 sm:px-0"
          onScroll={handleScroll}
        >
          {messages.length > 0 && (
            <div className="py-4 sm:py-6">
              {messages.map((message: any) => (
                <ChatMessage key={message.id} message={message} />
              ))}
            </div>
          )}
        </div>

        {/* Scroll to Bottom Button */}
        {showScrollToBottom && (
          <div className="absolute bottom-4 right-4 z-10">
            <Button
              onClick={() => scrollToBottom(true)}
              className="rounded-full w-12 h-12 bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg border border-border/20 transition-all duration-200 hover:scale-105"
              title="Scroll to bottom"
            >
              <ArrowDownIcon className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="p-3 sm:p-4">
        <MessageInput
          onSendMessage={onSendMessage}
          isLoading={isLoading}
          compact={false}
        />
      </div>
    </div>
  );
};

export default ChatInterface;
