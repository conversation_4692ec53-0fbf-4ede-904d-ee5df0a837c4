const n=e=>({connectionScreen:{inProgress:"En attente de confirmation",failed:"Connexion échouée",instruction:`Acceptez la demande de connexion dans ${e}`,retry:"Réessayer"},getStartedScreen:{instruction:`Scannez le code QR pour télécharger l'application ${e}`},scanScreen:{instruction:`Scannez le code QR avec l'application ${e} pour vous connecter`},getStartedLink:`Vous n'avez pas ${e} ?`,download:{chrome:"Télécharger l'extension Chrome",android:"Télécharger sur Google Play",iOS:"Télécharger sur l'App Store"}});export{n as default};
