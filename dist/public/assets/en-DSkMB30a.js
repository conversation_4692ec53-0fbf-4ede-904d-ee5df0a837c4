const e={id:"en_US",signIn:"Sign in",defaultButtonTitle:"Connect",connecting:"Connecting",switchNetwork:"Switch Network",switchingNetwork:"Switching Network",defaultModalTitle:"Sign in",recommended:"Recommended",installed:"Installed",buy:"Buy",continueAsGuest:"Continue as guest",connectAWallet:"Connect a Wallet",newToWallets:"New to wallets?",getStarted:"Get started",guest:"Guest",send:"Send",receive:"Receive",currentNetwork:"Current network",switchAccount:"Switch Account",requestTestnetFunds:"Request Testnet Funds",transactions:"Transactions",payTransactions:"Fiat Transactions",walletTransactions:"Wallet Transactions",viewAllTransactions:"View All Transactions",backupWallet:"Backup Wallet",guestWalletWarning:"This is a temporary guest wallet. Backup wallet if you don't want to lose access to it",switchTo:"Switch to",connectedToSmartWallet:"Smart Account",confirmInWallet:"Confirm in wallet",disconnectWallet:"Disconnect Wallet",copyAddress:"Copy Address",personalWallet:"Personal Wallet",smartWallet:"Smart Wallet",or:"OR",goBackButton:"Back",passkeys:{title:"Passkeys",linkPasskey:"Link a Passkey"},welcomeScreen:{defaultTitle:"Your gateway to the decentralized world",defaultSubtitle:"Connect a wallet to get started"},agreement:{prefix:"By connecting, you agree to the",termsOfService:"Terms of Service",and:"&",privacyPolicy:"Privacy Policy"},networkSelector:{title:"Select Network",mainnets:"Mainnets",testnets:"Testnets",allNetworks:"All",addCustomNetwork:"Add Custom Network",inputPlaceholder:"Search Network or Chain ID",categoryLabel:{recentlyUsed:"Recently Used",popular:"Popular",others:"All Networks"},loading:"Loading",failedToSwitch:"Failed to switch network"},receiveFundsScreen:{title:"Receive Funds",instruction:"Copy the address to send funds to this wallet"},sendFundsScreen:{title:"Send Funds",submitButton:"Send",token:"Token",sendTo:"Send to",amount:"Amount",successMessage:"Transaction Successful",invalidAddress:"Invalid Address",noTokensFound:"No Tokens Found",searchToken:"Search or Paste token address",transactionFailed:"Transaction Failed",transactionRejected:"Transaction Rejected",insufficientFunds:"Insufficient Funds",selectTokenTitle:"Select a Token",sending:"Sending"},signatureScreen:{instructionScreen:{title:"Sign in",instruction:"Please sign the message request in your wallet to continue",signInButton:"Sign in",disconnectWallet:"Disconnect Wallet"},signingScreen:{title:"Signing In",prompt:"Signing the signature request in your wallet",promptForSafe:"Sign signature request in your wallet & approve transaction in Safe",approveTransactionInSafe:"Approve transaction in Safe",tryAgain:"Try Again",failedToSignIn:"Failed to Sign in",inProgress:"Awaiting Confirmation"}},manageWallet:{title:"Manage Wallet",linkedProfiles:"Linked Profiles",linkProfile:"Link a Profile",connectAnApp:"Connect an App",exportPrivateKey:"Export Private Key"},viewFunds:{title:"View Funds",viewNFTs:"View NFTs",viewTokens:"View Tokens",viewAssets:"View Assets"}};export{e as default};
