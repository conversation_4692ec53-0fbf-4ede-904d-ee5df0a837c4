const a=o=>({connectionScreen:{inProgress:"Aguardando Confirmação",failed:"Falha na conexão",instruction:`Aceite a solicitação de conexão no ${o}`,retry:"Tentar novamente"},getStartedScreen:{instruction:`Escaneie o código QR para baixar o aplicativo ${o}`},scanScreen:{instruction:`Escaneie o código QR com o aplicativo ${o} para conectar`},getStartedLink:`Não tem o ${o}?`,download:{chrome:"Baixar extensão para Chrome",android:"Baixar no Google Play",iOS:"Baixar na App Store"}});export{a as default};
