import{w as t,x as r,aO as s}from"./index-Cx0gGTCY.js";import{d as m}from"./decimals-PaIcWjzJ.js";const o="0x06fdde03",d=[],i=[{type:"string"}];async function y(c){return t({contract:c.contract,method:[o,d,i],params:[]})}async function u(c){return r(()=>y(c),{cacheKey:`${c.contract.chain.id}:${c.contract.address}:name`,cacheTime:Number.POSITIVE_INFINITY})}const T="0x95d89b41",h=[],N=[{type:"string"}];async function l(c){return t({contract:c.contract,method:[T,h,N],params:[]})}async function I(c){return r(()=>l(c),{cacheKey:`${c.contract.chain.id}:${c.contract.address}:symbol`,cacheTime:Number.POSITIVE_INFINITY})}async function b(c){if(s(c.contract.address))return{name:"Ether",symbol:"ETH",decimals:18,...c.contract.chain.nativeCurrency};try{const[a,e,n]=await Promise.all([u(c).catch(()=>""),I(c),m(c)]);return{name:a,symbol:e,decimals:n}}catch(a){throw new Error(`Invalid currency token: ${a}`)}}export{b as getCurrencyMetadata};
