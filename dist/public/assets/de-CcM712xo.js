const e={id:"de_DE",signIn:"Anmelden",defaultButtonTitle:"Wallet verbinden",connecting:"Anmelden",switchNetwork:"Netzwerk wechseln",switchingNetwork:"Netzwerk wechseln",defaultModalTitle:"Anmelden",recommended:"Empfohlen",installed:"Installiert",buy:"Kaufen",continueAsGuest:"Als Gast fortfahren",connectAWallet:"Wallet verbinden",newToWallets:'Was ist ein "Wallet"?',getStarted:"Loslegen",guest:"<PERSON><PERSON>",send:"Senden",receive:"Empfangen",currentNetwork:"Aktuelles Netzwerk",switchAccount:"Account wechseln",requestTestnetFunds:"Testnetz-Geld anfordern",transactions:"Transaktionen",payTransactions:"Fiat Transaktionen",walletTransactions:"Wallet Transaktionen",viewAllTransactions:"Alle Transaktionen anzeigen",backupWallet:"Wallet sichern",guestWalletWarning:"Dies ist ein temporäres Gast-Wallet. Si<PERSON><PERSON> das Wallet, wenn du den Zugriff darauf nicht verlieren möchtest",switchTo:"Wechsle zu",connectedToSmartWallet:"Smart Account",confirmInWallet:"Im Wallet bestätigen",disconnectWallet:"Wallet trennen",copyAddress:"Adresse kopieren",personalWallet:"Persönliches Wallet",smartWallet:"Smart Wallet",or:"Oder",goBackButton:"Zurück",passkeys:{title:"Passkeys",linkPasskey:"Passkey verknüpfen"},welcomeScreen:{defaultTitle:"Dein Tor zur dezentralen Welt",defaultSubtitle:"Verbinde ein Wallet, um loszulegen"},agreement:{prefix:"Durch die Verbindung stimmst du diesen zu:",termsOfService:"Nutzungsbedingungen",and:"&",privacyPolicy:"Datenschutzrichtlinien"},networkSelector:{title:"Netzwerk auswählen",mainnets:"Mainnets",testnets:"Testnets",allNetworks:"Alle",addCustomNetwork:"Eigenes Netzwerk hinzufügen",inputPlaceholder:"Netzwerk oder Chain ID suchen",categoryLabel:{recentlyUsed:"Zuletzt verwendet",popular:"Beliebt",others:"Alle Netzwerke"},loading:"Laden",failedToSwitch:"Netzwerkwechsel fehlgeschlagen"},receiveFundsScreen:{title:"Geld empfangen",instruction:"Kopiere die Wallet-Adresse, um Geld an dieses Wallet zu senden"},sendFundsScreen:{title:"Geld senden",submitButton:"Senden",token:"Token",sendTo:"Senden an",amount:"Betrag",successMessage:"Transaktion erfolgreich",invalidAddress:"Ungültige Adresse",noTokensFound:"Keine Token gefunden",searchToken:"Token suchen oder einfügen",transactionFailed:"Transaktion fehlgeschlagen",transactionRejected:"Transaktion abgelehnt",insufficientFunds:"Nicht genügend Guthaben",selectTokenTitle:"Token auswählen",sending:"Sende"},signatureScreen:{instructionScreen:{title:"Anmelden",instruction:"Signiere die Anmeldeanfrage in deinem Wallet",signInButton:"Anmelden",disconnectWallet:"Wallet trennen"},signingScreen:{title:"Signaturanfrage",prompt:"Signiere die Signaturanfrage in deinem Wallet",promptForSafe:"Signiere die Signaturanfrage in deinem Wallet, um fortzufahren",approveTransactionInSafe:"Transaktion im Safe bestätigen",tryAgain:"Erneut versuchen",failedToSignIn:"Anmeldung fehlgeschlagen",inProgress:"Warte auf Bestätigung"}},manageWallet:{title:"Wallet verwalten",connectAnApp:"App verbinden",linkProfile:"Profil verknüpfen",linkedProfiles:"Verknüpfte Profile",exportPrivateKey:"PrivateKey exportieren"},viewFunds:{title:"Guthaben anzeigen",viewNFTs:"NFTs anzeigen",viewTokens:"Tokens anzeigen",viewAssets:"Assets anzeigen"}};export{e as default};
