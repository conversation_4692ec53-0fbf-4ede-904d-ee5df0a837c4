const a=e=>({connectionScreen:{inProgress:"Esperando confirmación",failed:"Conexión fallida",instruction:`Acepta la solicitud de conexión en tu cartera ${e}`,retry:"Intentar de nuevo"},getStartedScreen:{instruction:`Escanea el código QR para descargar la aplicación ${e}`},scanScreen:{instruction:`Escanea el código QR con la aplicación de cartera ${e} para conectarte`},getStartedLink:`¿No tienes la cartera ${e}?`,download:{chrome:"Descargar extensión para Chrome",android:"Descargar en Google Play",iOS:"Descargar en App Store"}});export{a as default};
