const A="data:image/webp;base64,UklGRuoFAABXRUJQVlA4WAoAAAAQAAAAfwAAfwAAQUxQSK0AAAABcFvbVlTdZQ+U8szpgMamlh6pmS2wMNOQTPhzdqM/RcQE4Kn45TgHy/NYvOB145KW1mn0VjQ588oct8G3Rnky2Tw4q50e2HWwXu2d5ME7C2DiYB4NXKW2OUmDewpKTpdCruyNXDs7uT7+/v/7/+//d+Czk+t7I9eWQq4EJaeSyCW4jVp1MJFaNIBkYlkAwK60Vov7ySqpbKcHgMRKqMYZLxqXtLROo7eiyRncAwBWUDggFgUAAPAcAJ0BKoAAgAA+bTCWRyQioiEpErpwgA2JYgDXHaFf3mN9h/Kr2fK7/e/v1xX9uf7T7mfgN6qfMA/Tn/JdVPzAfsr+xHYz/zHqAf2P+4dZz6AHlneyT+33pAf//OWv7HaRxmdwstPTEzTPKG6la9m62Kx50/nt/ixUS3Z3lxhkf6FZdf4T4X6mxg3kkxOnf6rcpJbsKYNw8qqQGTszbQy7F3AwUhj6i/hOao8vhFoO/7BjLf0DNVL++IP6UADmpacbdNor5u0EUm+q17ElnypezV7SCuhJGwhWDhSlrLRvPq8FzF71yImmxZCGPVkaAAD+/EoAAdzq1PC5QkDaX2Ptr/Pmr4pxC7l6y6BRoC4sz/9OWdQeN/ddF7Uy1Qa/me6dRW48+kTTXi9Fyn2UOZYfiuIEu0Nual7EhaLGwgaa3wSXO8KNfcgtB6JbDE+aYnutSqf2Kc5jrYt6IIfm1cxObVSIckbI1QO+SqrNKDR++Zp7YbnrZc8NqJA4f/IofMTnUNeZ8YgSAym/0f8TH8z7eR+crFrirg/do/dd18nuvi4w/AJ/WwEXW+L62hxns4ja+mWfJ9eQSuHDycGw5XBV4PwvQVbK7I8WdD1If55OuF/bFkNDKsfhrmcwJBjLxkY2dunnTCarKWv1GvEjrrubqdtC484vpOj8sp5kBDxHYB0jtH1PP28MkjO24OKlxXmsz6T7bhIC8GniXjdxRYn2b8AGCzb6tL6qk1j09Vb8z5fSoBvOpIb6pCiBK+da+4Rey1ZiSOG6h1l15g2++aSBHcAEgsGvddx7f8RWjfv++KCbhPADNqUp4aTCMjspDe0QgqYeSG4O4yjdmUYjY7M3HbECe8DlZup0ir02YMoeaCK/iDXrjfnlOkOxdqE4WL69KsgGASbEYULSkHJu4T/IzaJEzCUfTES0wj0z7P/Cz6dgr7YBmkCGrpxNLsf9KbvzrXBZjIz9IEtZQU4C+Ygv4G5VK7OAkmEQM+mTf/47/qPCmf/LUFXLVlI+k5mlrUW3kWCdN2j43XWnTNevvCs96SQa8aLRXYa2PfutzAerT8x5zvyMfyNeAmiYKZv5S3swlB5Tw5KLj4exsteM9SlfAUGTyidud8Uu2eG3C/w0rIl4Horl8MzeGwNOysir+d94jr9tqnYwHS9nDFQzlw9MNhPmN9Ll04/sTxm+Gf+XB8tllZHQLjqi89uRBj6BEnaG79nG6GyHV7DjnwbZ7NGFc3312lFjTaCP9tOIDUFDlW5RtfffSau1jL1GBNvSo9vyCO2Ypp8epP4gZeF2C03c9YinvruAcW1tD0opTYsPriXkr9tS8fbxdykrMZ4nafyf/9foPlZGpGgmY1ROwbW2Hr2NxBM4MOgSIWNGYhBFOdyHrk8wFiFVgaqdBmWJkzrnrEQKZcHOk44MPDifi52MCw+syHFc4BUGhTGYUvQmf4TN7dFCv56vaurjot3vnZ6zWMaerOGwKB0Sk9drQeiupTgRAfe4eL+b+o/QqN0GHtHWUFiK+GFJXCGjtvKkB5zr/rw6cpYlScyMfWQeJoDBbg7kBLhpli3s9INDBrz/OzPqxpBuhBypx5TB4kayIgo+CSFIcsxEQOyqjytV0JCjL0oUWziX0JQgQuAzI/wt4mr3czK4oU2yQJR529gSr3qtam1pmAXXC3bcaCB3WRbNIT1UM53D61rvtsbDRocsi9xQAhAUkBPJRfwxRTn0lAAAAAAAAA==";export{A as default};
