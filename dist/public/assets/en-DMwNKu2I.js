const o=n=>({connectionScreen:{inProgress:"Awaiting Confirmation",failed:"Connection failed",instruction:`Accept the connection request in ${n}`,retry:"Try Again"},getStartedScreen:{instruction:`Scan the QR code to download the ${n} app`},scanScreen:{instruction:`Scan the QR code with the ${n} app to connect`},getStartedLink:`Don't have ${n}?`,download:{chrome:"Download Chrome Extension",android:"Download on Google Play",iOS:"Download on App Store"}});export{o as default};
