import{H as c,I as i,J as s,K as d,L as t,W as o,M as u,O as m}from"./index-Cx0gGTCY.js";function W(e){const{screen:l}=c(),n=i(),a=s(e.connectLocale.id);return e.size==="wide"||l!==d.main&&e.size==="compact"?t.jsx(o,{wallet:e.wallet,selectWallet:()=>{n({}),e.select()},client:e.client,connectLocale:e.connectLocale,recommendedWallets:e.recommendedWallets,isActive:l===e.wallet,badge:void 0}):a?t.jsx(m,{disabled:e.disabled,locale:a,wallet:e.wallet,done:e.done,select:e.select,goBack:e.goBack,chain:e.chain,client:e.client,size:e.size}):t.jsx(u,{height:"195px"})}export{W as default};
