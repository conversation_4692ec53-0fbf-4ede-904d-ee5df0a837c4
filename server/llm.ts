import { Message } from "@shared/schema";
import {
  sendNebulaMessage,
  generateSessionId,
  NebulaContextFilter,
} from "./nebulaClient";
import * as fs from "fs";
import * as path from "path";

/**
 * Transform responses to replace "Nebula" with "Web3AI"
 * This function is duplicated from nebulaClient.ts to avoid circular imports
 */
function transformResponseForWeb3AI(message: string): string {
  if (!message) return message;

  // Replace various forms of "Nebula" with "Web3AI"
  let transformedMessage = message
    // Direct replacements
    .replace(/\bNebula\b/g, "Web3AI")
    .replace(/\bnebula\b/g, "Web3AI")
    .replace(/\bNEBULA\b/g, "WEB3AI")

    // Handle possessive forms
    .replace(/\bNebula's\b/g, "Web3AI's")
    .replace(/\bnebula's\b/g, "Web3AI's")

    // Handle specific thirdweb references if they appear
    .replace(/\bthirdweb's Nebula\b/g, "Web3AI")
    .replace(/\bthirdweb Nebula\b/g, "Web3AI")

    // Handle "I am Nebula" type statements
    .replace(/\bI am Nebula\b/g, "I am Web3AI")
    .replace(/\bI'm Nebula\b/g, "I'm Web3AI")

    // Handle platform identification
    .replace(/\bNebula platform\b/g, "Web3AI platform")
    .replace(/\bNebula AI\b/g, "Web3AI")
    .replace(/\bNebula assistant\b/g, "Web3AI assistant");

  return transformedMessage;
}

interface AIResponse {
  text: string;
  source: string;
  executionTime: number;
  blockchainData?: any;
}

/**
 * Generate a response from Nebula API for intelligent blockchain assistance
 */
export async function generateChatResponse(
  chatHistory: Message[],
  chainId: string,
  sessionId: string,
  walletAddress?: string
): Promise<AIResponse> {
  const lastUserMessage = [...chatHistory]
    .reverse()
    .find((msg) => msg.role === "user");

  if (!lastUserMessage) {
    throw new Error("No user message found in history");
  }

  // Start timing execution
  const startTime = Date.now();

  // Check if this is a fallback session ID (when Nebula API is unavailable)
  const isFallbackSession = sessionId.startsWith("fallback_");

  try {
    // Only use Nebula API if we have a real session ID
    if (!isFallbackSession) {
      // Use Nebula API for intelligent blockchain responses with wallet and chain context
      const enhancedMessage = `On ${getChainName(chainId)} network: ${
        lastUserMessage.content
      }`;

      // Create context filter with wallet and chain information
      const contextFilter: NebulaContextFilter = {
        chainIds: [parseInt(chainId)],
      };

      // Add wallet address to context if provided
      if (walletAddress) {
        contextFilter.walletAddresses = [walletAddress];
      }

      const nebulaResponse = await sendNebulaMessage(
        enhancedMessage,
        sessionId, // Use the provided session ID
        false,
        contextFilter
      );

      const executionTime = Date.now() - startTime;

      // Transform the response to replace "Nebula" with "Web3AI"
      const transformedMessage = transformResponseForWeb3AI(
        nebulaResponse.message || "Response received from Web3AI"
      );

      return {
        text: transformedMessage,
        source: `Web3AI on ${getChainName(chainId)}`,
        executionTime: executionTime,
        blockchainData: {
          sessionId: nebulaResponse.session_id,
          chainId: chainId,
          enhanced: true,
          actions: nebulaResponse.actions || [],
          transactions: nebulaResponse.transactions || [],
        },
      };
    } else {
      // Use fallback response for fallback sessions
      throw new Error("Using fallback session - skipping Nebula API");
    }
  } catch (error) {
    console.error("Error calling Nebula API or using fallback session:", error);

    // Fallback to enhanced local responses if Nebula API fails
    const executionTime = Date.now() - startTime;
    return generateFallbackResponse(
      lastUserMessage.content,
      chainId,
      executionTime
    );
  }
}

/**
 * Enhanced fallback response when Nebula API is unavailable
 */
function generateFallbackResponse(
  query: string,
  chainId: string,
  fallbackExecutionTime: number
): AIResponse {
  const userQuery = query.toLowerCase();
  let responseText = "";
  let blockchainData = null;

  // Simple web3 topics detection to simulate LLM understanding
  if (userQuery.includes("gas") || userQuery.includes("fee")) {
    responseText = createGasFeeResponse(chainId);
    blockchainData = {
      low: 35,
      average: 42,
      high: 55,
      unit: "Gwei",
    };
  } else if (userQuery.includes("launch") && userQuery.includes("token")) {
    responseText = createTokenLaunchResponse(chainId);
  } else if (userQuery.includes("buy") && userQuery.includes("usdc")) {
    responseText = createBuyUSDCResponse(chainId);
  } else if (userQuery.includes("analyze") || userQuery.includes("contract")) {
    responseText = createContractAnalysisResponse();
  } else if (
    userQuery.includes("send") &&
    (userQuery.includes("eth") || userQuery.includes("token"))
  ) {
    responseText = createSendTokenResponse(chainId);
  } else if (userQuery.includes("what") && userQuery.includes("do")) {
    responseText = createCapabilitiesResponse();
  } else {
    responseText = createGenericResponse(userQuery);
  }

  return {
    text: responseText,
    source: `Enhanced Assistant on ${getChainName(chainId)}`,
    executionTime: fallbackExecutionTime,
    blockchainData,
  };
}

function getChainName(chainId: string): string {
  const chainNames: Record<string, string> = {
    // Ethereum and Layer 2s
    "1": "Ethereum Mainnet",
    "42161": "Arbitrum One",
    "10": "Optimism",
    "8453": "Base",

    // Alternative Layer 1s
    "137": "Polygon",
    "56": "Binance Smart Chain",
    "43114": "Avalanche C-Chain",
    "250": "Fantom Opera",

    // Testnets
    "11155111": "Sepolia Testnet",
    "421614": "Arbitrum Sepolia",
    "11155420": "Optimism Sepolia",
    "84532": "Base Sepolia",
    "80002": "Polygon Amoy Testnet",
    "97": "BSC Testnet",

    // Legacy testnets (keeping for backward compatibility)
    "5": "Goerli Testnet",
    "80001": "Mumbai Testnet",
  };

  return chainNames[chainId] || "Unknown Chain";
}

function createGasFeeResponse(chainId: string): string {
  return `To check current gas fees on ${getChainName(
    chainId
  )}, you have several options:

1. Use blockchain explorers like Etherscan, which shows real-time gas prices
2. Check gas tracking websites like ETH Gas Station or Gas Now
3. Use wallet applications (MetaMask shows current gas estimates)
4. Call the Ethereum JSON-RPC method \`eth_gasPrice\`

Current ${getChainName(chainId)} Gas Prices:
- Low: 35 Gwei
- Average: 42 Gwei
- High: 55 Gwei

Would you like me to help you estimate gas for a specific transaction type?`;
}

function createTokenLaunchResponse(chainId: string): string {
  return `Launching a token on ${getChainName(chainId)} involves several steps:

1. **Create the Token Contract**: You can use thirdweb's TokenDrop or Token contract templates for this.

2. **Deploy the Contract**: Use the thirdweb dashboard or SDK to deploy to ${getChainName(
    chainId
  )}.

\`\`\`javascript
// Example code to deploy a token with thirdweb SDK
import { ThirdwebSDK } from "@thirdweb-dev/sdk";

const sdk = ThirdwebSDK.fromPrivateKey(
  process.env.PRIVATE_KEY,
  "${chainId}" // ${getChainName(chainId)}
);

const tokenContract = await sdk.deployer.deployToken({
  name: "My Token",
  symbol: "MTK",
  primary_sale_recipient: "0x...",
});

console.log("Token contract deployed at:", tokenContract.getAddress());
\`\`\`

3. **Configure Token Properties**: Set supply, minting rules, etc.
4. **Add Liquidity**: Create trading pairs on DEXs
5. **Verify the Contract**: Ensure your code is verified on block explorers

Would you like me to guide you through any specific step in more detail?`;
}

function createBuyUSDCResponse(chainId: string): string {
  return `To buy USDC on ${getChainName(chainId)}, you have several options:

1. **Centralized Exchanges**: Exchanges like Coinbase, Binance, or Kraken allow direct purchases of USDC with fiat currencies.

2. **Decentralized Exchanges (DEXs)**: You can swap other cryptocurrencies for USDC on:
   - Uniswap
   - SushiSwap
   - Curve Finance

3. **Using thirdweb SDK**: You can programmatically swap for USDC with code:

\`\`\`javascript
import { ThirdwebSDK } from "@thirdweb-dev/sdk";

// Initialize SDK
const sdk = new ThirdwebSDK("${chainId}");

// Connect wallet
const wallet = await sdk.wallet.connect();

// Get the ERC20 contract for USDC
const usdcAddress = "******************************************"; // USDC on Ethereum
const usdcContract = await sdk.getContract(usdcAddress);

// Swap ETH for USDC via router
const routerAddress = "0x..."; // DEX router address
const routerContract = await sdk.getContract(routerAddress);

// Approve the transaction
await routerContract.call("swapExactETHForTokens", {
  value: ethers.utils.parseEther("0.1"), // Amount of ETH to swap
  // Other parameters like minimum amount out, path, recipient, deadline
});
\`\`\`

Would you like step-by-step instructions for a specific method?`;
}

function createContractAnalysisResponse(): string {
  return `To analyze Uniswap contracts, you can:

1. **Fetch the Contract ABI**: This can be done using block explorers or thirdweb

2. **Analyze Core Functions**: Key Uniswap contracts include:
   - Factory contract: Creates pairs
   - Router contract: Handles trading transactions
   - Pair contracts: Individual trading pairs

3. **Using thirdweb SDK**:

\`\`\`javascript
import { ThirdwebSDK } from "@thirdweb-dev/sdk";

// Initialize the SDK
const sdk = new ThirdwebSDK("ethereum");

// Get Uniswap V2 Factory contract
const factoryAddress = "******************************************";
const factoryContract = await sdk.getContract(factoryAddress);

// Get all created pairs
const allPairsLength = await factoryContract.call("allPairsLength");
console.log("Total Uniswap V2 pairs:", allPairsLength);

// Get a specific pair
const pairAddress = await factoryContract.call("getPair", [
  "0xTokenA", // Token A address
  "0xTokenB"  // Token B address
]);

// Get pair contract and analyze
const pairContract = await sdk.getContract(pairAddress);
const reserves = await pairContract.call("getReserves");
console.log("Pair reserves:", reserves);
\`\`\`

Would you like me to analyze a specific aspect of Uniswap contracts?`;
}

function createSendTokenResponse(chainId: string): string {
  return `To send ETH to someone on ${getChainName(chainId)}, you can:

1. **Using a Wallet**: The simplest way is to use MetaMask, Trust Wallet, etc.

2. **Using thirdweb SDK**:

\`\`\`javascript
import { ThirdwebSDK } from "@thirdweb-dev/sdk";
import { ethers } from "ethers";

// Initialize the SDK with your private key (server-side) or with Web3 provider (client-side)
const sdk = ThirdwebSDK.fromPrivateKey(
  process.env.PRIVATE_KEY, // Your private key
  "${chainId}" // ${getChainName(chainId)}
);

// For client-side with connected wallet
// const sdk = new ThirdwebSDK("${chainId}");
// await sdk.wallet.connect();

// Send ETH
const recipientAddress = "0x..."; // Recipient's address
const amountInEther = "0.1"; // Amount to send

// Convert amount to wei (smallest unit)
const amountInWei = ethers.utils.parseEther(amountInEther);

// Send the transaction
const tx = await sdk.wallet.transfer(
  recipientAddress,
  amountInWei
);

console.log("Transaction hash:", tx.receipt.transactionHash);
\`\`\`

3. **For ERC-20 Tokens**:

\`\`\`javascript
// Get the token contract
const tokenAddress = "0x..."; // Token contract address
const tokenContract = await sdk.getContract(tokenAddress);

// Transfer tokens
await tokenContract.erc20.transfer(
  recipientAddress,
  amountToSend
);
\`\`\`

Would you like more details on any of these methods?`;
}

function createCapabilitiesResponse(): string {
  try {
    // Path to the markdown file - handle both development and production paths
    const markdownPath = path.join(
      process.cwd(),
      "client/src/data/web3ai-capabilities.md"
    );

    // Read the markdown file
    const markdownContent = fs.readFileSync(markdownPath, "utf-8");

    // Return the content as-is (markdown formatting will be preserved in chat)
    return markdownContent;
  } catch (error) {
    console.error("Error reading web3ai-capabilities.md:", error);

    // Fallback to original response if file can't be read
    return `I'm Web3AI, your intelligent blockchain assistant! Here's what I can help you with:

1. **Smart Contract Interactions**:
   - Deploy and interact with contracts
   - Analyze contract code
   - Troubleshoot contract issues

2. **Wallet Operations**:
   - Help connect wallets
   - Assist with transfers
   - Explain gas fees and optimize transactions

3. **Token Actions**:
   - Guide you through token launches
   - Help with token swaps and purchases
   - Explain tokenomics concepts

4. **Development Support**:
   - Provide code snippets for blockchain interactions
   - Explain web3 development concepts
   - Help debug web3 applications

5. **Chain Information**:
   - Provide data about different blockchains
   - Track gas prices and network conditions
   - Compare chain features and compatibility

Just ask me questions about any blockchain topic, and I'll assist you with information, code examples, and actionable guidance!`;
  }
}

function createGenericResponse(query: string): string {
  return `I understand you're asking about "${query}". To help you with blockchain-related queries, I can:

1. Provide information about various blockchain networks
2. Help with smart contract deployment and interaction
3. Assist with wallet operations like sending tokens
4. Explain web3 concepts and development approaches
5. Generate code examples using thirdweb SDK

Could you provide more specific details about what you'd like to know about "${query}" in the blockchain context?`;
}
